<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => 'PLAQUES ONDULÉES EN FIBROCIMENT',
    'subtitle' => 'Solutions durables et esthétiques pour vos projets de construction',
    'backgroundImage' => 'assets/images/hero-background.jpg'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => 'PLAQUES ONDULÉES EN FIBROCIMENT',
    'subtitle' => 'Solutions durables et esthétiques pour vos projets de construction',
    'backgroundImage' => 'assets/images/hero-background.jpg'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<section class="relative h-screen flex items-center justify-center overflow-hidden">
    <!-- Background Image -->
    <div class="absolute inset-0">
        <img src="<?php echo e(asset($backgroundImage)); ?>" 
             alt="<?php echo e($title); ?>" 
             class="w-full h-full object-cover">
        <!-- Dark overlay for better text readability -->
        <div class="absolute inset-0 bg-gradient-to-r from-blue-900/70 to-blue-700/50"></div>
    </div>
    
    <!-- Content -->
    <div class="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
        <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 tracking-tight leading-tight">
            <?php echo e($title); ?>

        </h1>
        
        <?php if($subtitle): ?>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90 leading-relaxed">
            <?php echo e($subtitle); ?>

        </p>
        <?php endif; ?>
        
        <!-- Call to Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#produits" 
               class="bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                Découvrir nos produits
            </a>
            <a href="#contact" 
               class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-900 transition-all duration-300 transform hover:scale-105">
                Nous contacter
            </a>
        </div>
    </div>
    
    <!-- Scroll indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Desktop\PROJETS\projet-baccanale\resources\views/components/frontend/hero.blade.php ENDPATH**/ ?>