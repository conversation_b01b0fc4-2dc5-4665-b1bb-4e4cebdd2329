@import 'tailwindcss';
@import '../../vendor/livewire/flux/dist/flux.css';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --color-zinc-50: #fafafa;
    --color-zinc-100: #f5f5f5;
    --color-zinc-200: #e5e5e5;
    --color-zinc-300: #d4d4d4;
    --color-zinc-400: #a3a3a3;
    --color-zinc-500: #737373;
    --color-zinc-600: #525252;
    --color-zinc-700: #404040;
    --color-zinc-800: #262626;
    --color-zinc-900: #171717;
    --color-zinc-950: #0a0a0a;

    --color-accent: var(--color-neutral-800);
    --color-accent-content: var(--color-neutral-800);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-white);
        --color-accent-content: var(--color-white);
        --color-accent-foreground: var(--color-neutral-800);
    }
}

@layer base {

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

[data-flux-field]:not(ui-radio, ui-checkbox) {
    @apply grid gap-2;
}

[data-flux-label] {
    @apply  !mb-0 !leading-tight;
}

input:focus[data-flux-control],
textarea:focus[data-flux-control],
select:focus[data-flux-control] {
    @apply outline-hidden ring-2 ring-accent ring-offset-2 ring-offset-accent-foreground;
}

/* \[:where(&)\]:size-4 {
    @apply size-4;
} */

/* ===================================
   BACCANALE CUSTOM STYLES
   =================================== */

@layer components {
    /* ===== BRAND COLORS ===== */
    .bg-baccanale-blue {
        background-color: #5B9BD5;
    }

    .bg-baccanale-blue-dark {
        background-color: #4A8BC2;
    }

    .bg-baccanale-blue-light {
        background-color: #7BAEE0;
    }

    .text-baccanale-blue {
        color: #5B9BD5;
    }

    .text-baccanale-blue-dark {
        color: #4A8BC2;
    }

    .border-baccanale-blue {
        border-color: #5B9BD5;
    }

    /* ===== HOVER STATES ===== */
    .hover\:bg-baccanale-blue-dark:hover {
        background-color: #4A8BC2;
    }

    .hover\:text-baccanale-blue:hover {
        color: #5B9BD5;
    }

    .hover\:text-baccanale-blue-dark:hover {
        color: #4A8BC2;
    }

    /* ===== LOGO STYLES ===== */
    .logo-nav {
        @apply h-10 w-auto object-contain transition-all duration-200;
        filter: brightness(1.05) contrast(1.05);
    }

    .logo-nav:hover {
        filter: brightness(1.1) contrast(1.1);
        transform: scale(1.02);
    }

    .logo-footer {
        @apply h-12 w-auto object-contain;
        filter: brightness(1.15) contrast(1.05);
    }

    /* ===== HERO SECTION ===== */
    .hero-bg {
        @apply w-full h-full object-cover;
        filter: brightness(0.75) contrast(1.1) saturate(1.1);
    }

    /* ===== UTILITY CLASSES ===== */
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .aspect-w-16 {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
    }

    .aspect-h-9 {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    /* ===== ANIMATIONS ===== */
    .animate-fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }

    .animate-slide-up {
        animation: slideUp 0.8s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* ===== COMPONENT SPECIFIC STYLES ===== */
    .card-hover {
        @apply transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl;
    }

    .btn-primary {
        @apply bg-baccanale-blue text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-baccanale-blue-dark hover:shadow-lg transform hover:scale-105;
    }

    .btn-secondary {
        @apply border-2 border-baccanale-blue text-baccanale-blue px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-baccanale-blue hover:text-white hover:shadow-lg transform hover:scale-105;
    }
}
