<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'products' => collect(),
    'title' => 'Nos Produits Phares',
    'subtitle' => 'Découvrez notre gamme complète de plaques ondulées en fibrociment'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'products' => collect(),
    'title' => 'Nos Produits Phares',
    'subtitle' => 'Découvrez notre gamme complète de plaques ondulées en fibrociment'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<section id="produits" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                <?php echo e($title); ?>

            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                <?php echo e($subtitle); ?>

            </p>
        </div>
        
        <!-- Products Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <!-- Product Image -->
                <?php if($product->images->count() > 0): ?>
                    <div class="aspect-w-16 aspect-h-9 bg-gray-200">
                        <img src="<?php echo e(asset('storage/' . $product->images->first()->image_path)); ?>" 
                             alt="<?php echo e($product->name); ?>" 
                             class="w-full h-48 object-cover">
                    </div>
                <?php else: ?>
                    <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                        <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                <?php endif; ?>
                
                <!-- Product Content -->
                <div class="p-6">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded">
                            <?php echo e($product->category->name ?? 'Non catégorisé'); ?>

                        </span>
                        <span class="text-lg font-bold text-gray-900">
                            <?php echo e(number_format($product->price, 2)); ?>€
                        </span>
                    </div>
                    
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        <?php echo e($product->name); ?>

                    </h3>
                    
                    <p class="text-gray-600 mb-4 line-clamp-3">
                        <?php echo e(Str::limit($product->description, 120)); ?>

                    </p>
                    
                    <a href="#" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                        En savoir plus
                        <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <!-- Empty State -->
            <div class="col-span-full text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun produit disponible</h3>
                <p class="text-gray-600">Nos produits seront bientôt disponibles.</p>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- View All Button -->
        <?php if($products->count() > 0): ?>
        <div class="text-center mt-12">
            <a href="#" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-300 inline-flex items-center">
                Voir tous nos produits
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Desktop\PROJETS\projet-baccanale\resources\views/components/frontend/sections/products.blade.php ENDPATH**/ ?>