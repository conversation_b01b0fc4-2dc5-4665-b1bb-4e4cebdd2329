<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => 'Prêt à démarrer votre projet ?',
    'subtitle' => 'Contactez nos experts pour obtenir un devis personnalisé et des conseils adaptés à vos besoins',
    'primaryButtonText' => 'Demander un devis',
    'primaryButtonUrl' => '#contact',
    'secondaryButtonText' => 'Nous appeler',
    'secondaryButtonUrl' => 'tel:+33123456789'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => 'Prêt à démarrer votre projet ?',
    'subtitle' => 'Contactez nos experts pour obtenir un devis personnalisé et des conseils adaptés à vos besoins',
    'primaryButtonText' => 'Demander un devis',
    'primaryButtonUrl' => '#contact',
    'secondaryButtonText' => 'Nous appeler',
    'secondaryButtonUrl' => 'tel:+33123456789'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<section class="py-20 bg-blue-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
            <?php echo e($title); ?>

        </h2>
        
        <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
            <?php echo e($subtitle); ?>

        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e($primaryButtonUrl); ?>" 
               class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <?php echo e($primaryButtonText); ?>

            </a>
            
            <a href="<?php echo e($secondaryButtonUrl); ?>" 
               class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <?php echo e($secondaryButtonText); ?>

            </a>
        </div>
        
        <!-- Additional Info -->
        <div class="mt-8 text-blue-100 text-sm">
            <p>Réponse sous 24h • Devis gratuit • Conseil personnalisé</p>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Desktop\PROJETS\projet-baccanale\resources\views/components/frontend/sections/cta.blade.php ENDPATH**/ ?>