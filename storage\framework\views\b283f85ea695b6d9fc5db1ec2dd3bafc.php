

<?php $__env->startSection('title', 'Accueil'); ?>
<?php $__env->startSection('description', 'Baccanale SARL - Spécialiste des plaques ondulées en fibrociment de haute qualité. Solutions durables et esthétiques pour tous vos projets de construction.'); ?>
<?php $__env->startSection('keywords', 'plaques ondulées, fibrociment, construction, toiture, Baccanale, matériaux construction'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section -->
    <?php if (isset($component)) { $__componentOriginald3ef3bf91c913f7f4eb45956c072854e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald3ef3bf91c913f7f4eb45956c072854e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.frontend.hero','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('frontend.hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald3ef3bf91c913f7f4eb45956c072854e)): ?>
<?php $attributes = $__attributesOriginald3ef3bf91c913f7f4eb45956c072854e; ?>
<?php unset($__attributesOriginald3ef3bf91c913f7f4eb45956c072854e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald3ef3bf91c913f7f4eb45956c072854e)): ?>
<?php $component = $__componentOriginald3ef3bf91c913f7f4eb45956c072854e; ?>
<?php unset($__componentOriginald3ef3bf91c913f7f4eb45956c072854e); ?>
<?php endif; ?>

    <!-- Features Section -->
    <?php if (isset($component)) { $__componentOriginal45ffdac61eac1842adf24ca85978a029 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal45ffdac61eac1842adf24ca85978a029 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.frontend.sections.features','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('frontend.sections.features'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal45ffdac61eac1842adf24ca85978a029)): ?>
<?php $attributes = $__attributesOriginal45ffdac61eac1842adf24ca85978a029; ?>
<?php unset($__attributesOriginal45ffdac61eac1842adf24ca85978a029); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal45ffdac61eac1842adf24ca85978a029)): ?>
<?php $component = $__componentOriginal45ffdac61eac1842adf24ca85978a029; ?>
<?php unset($__componentOriginal45ffdac61eac1842adf24ca85978a029); ?>
<?php endif; ?>

    <!-- Products Section -->
    <?php if (isset($component)) { $__componentOriginalc00c48f36fce9e299631e4bdfbcff1c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc00c48f36fce9e299631e4bdfbcff1c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.frontend.sections.products','data' => ['products' => $featuredProducts]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('frontend.sections.products'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['products' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($featuredProducts)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc00c48f36fce9e299631e4bdfbcff1c0)): ?>
<?php $attributes = $__attributesOriginalc00c48f36fce9e299631e4bdfbcff1c0; ?>
<?php unset($__attributesOriginalc00c48f36fce9e299631e4bdfbcff1c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc00c48f36fce9e299631e4bdfbcff1c0)): ?>
<?php $component = $__componentOriginalc00c48f36fce9e299631e4bdfbcff1c0; ?>
<?php unset($__componentOriginalc00c48f36fce9e299631e4bdfbcff1c0); ?>
<?php endif; ?>

    <!-- CTA Section -->
    <?php if (isset($component)) { $__componentOriginal7f0c8c65d835ce40eee81264709f4ec9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f0c8c65d835ce40eee81264709f4ec9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.frontend.sections.cta','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('frontend.sections.cta'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f0c8c65d835ce40eee81264709f4ec9)): ?>
<?php $attributes = $__attributesOriginal7f0c8c65d835ce40eee81264709f4ec9; ?>
<?php unset($__attributesOriginal7f0c8c65d835ce40eee81264709f4ec9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f0c8c65d835ce40eee81264709f4ec9)): ?>
<?php $component = $__componentOriginal7f0c8c65d835ce40eee81264709f4ec9; ?>
<?php unset($__componentOriginal7f0c8c65d835ce40eee81264709f4ec9); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\PROJETS\projet-baccanale\resources\views/frontend/home.blade.php ENDPATH**/ ?>