@extends('layouts.frontend')

@section('title', 'Accueil')

@section('content')
<!-- Hero Section -->
<section class="relative h-screen flex items-center justify-center overflow-hidden">
    <!-- Background Image -->
    <div class="absolute inset-0">
        <img src="{{ asset('assets/images/hero-background.jpg') }}" alt="Plaques ondulées en fibrociment" class="hero-bg">
        <!-- Dark overlay for better text readability -->
        <div class="absolute inset-0 bg-gradient-to-r from-blue-900/70 to-blue-700/50"></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 tracking-tight">
            PLAQUES ONDULÉES EN FIBROCIMENT
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
            Solutions durables et esthétiques pour vos projets de construction
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#produits" class="bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors duration-300">
                Découvrir nos produits
            </a>
            <a href="#contact" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-900 transition-colors duration-300">
                Nous contacter
            </a>
        </div>
    </div>

    <!-- Scroll indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Pourquoi choisir nos plaques ondulées ?
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Des matériaux de qualité supérieure pour des constructions durables et esthétiques
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center p-6">
                <div class="w-16 h-16 bg-baccanale-blue rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Qualité Garantie</h3>
                <p class="text-gray-600">
                    Matériaux certifiés et testés pour une durabilité exceptionnelle dans le temps.
                </p>
            </div>

            <div class="text-center p-6">
                <div class="w-16 h-16 bg-baccanale-blue rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Installation Rapide</h3>
                <p class="text-gray-600">
                    Système de pose optimisé pour une installation efficace et professionnelle.
                </p>
            </div>

            <div class="text-center p-6">
                <div class="w-16 h-16 bg-baccanale-blue rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Résistance Climatique</h3>
                <p class="text-gray-600">
                    Conçues pour résister aux intempéries et aux conditions climatiques extrêmes.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Products Section -->
<section id="produits" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Nos Produits Phares
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Découvrez notre gamme complète de plaques ondulées en fibrociment
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @forelse($featuredProducts as $product)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                @if($product->images->count() > 0)
                    <img src="{{ asset('storage/' . $product->images->first()->image_path) }}"
                         alt="{{ $product->name }}"
                         class="w-full h-48 object-cover">
                @else
                    <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                        <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                @endif

                <div class="p-6">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-baccanale-blue font-medium">{{ $product->category->name ?? 'Non catégorisé' }}</span>
                        <span class="text-lg font-bold text-gray-900">{{ number_format($product->price, 2) }}€</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $product->name }}</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">{{ Str::limit($product->description, 120) }}</p>
                    <a href="#" class="inline-flex items-center text-baccanale-blue hover:text-baccanale-blue-dark font-medium">
                        En savoir plus
                        <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
            @empty
            <div class="col-span-full text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun produit disponible</h3>
                <p class="text-gray-600">Nos produits seront bientôt disponibles.</p>
            </div>
            @endforelse
        </div>

        <div class="text-center mt-12">
            <a href="#" class="bg-baccanale-blue text-white px-8 py-3 rounded-lg font-semibold hover:bg-baccanale-blue-dark transition-colors duration-300">
                Voir tous nos produits
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-baccanale-blue">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
            Prêt à démarrer votre projet ?
        </h2>
        <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Contactez nos experts pour obtenir un devis personnalisé et des conseils adaptés à vos besoins
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#contact" class="bg-white text-baccanale-blue px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors duration-300">
                Demander un devis
            </a>
            <a href="tel:+33123456789" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-baccanale-blue transition-colors duration-300">
                Nous appeler
            </a>
        </div>
    </div>
</section>
@endsection