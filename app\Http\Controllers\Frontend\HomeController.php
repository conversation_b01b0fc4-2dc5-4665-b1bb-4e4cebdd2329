<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        // Récupérer les produits en vedette (par exemple les 6 derniers)
        $featuredProducts = Product::with('category', 'images')
            ->latest()
            ->take(6)
            ->get();

        // Récupérer les catégories principales
        $categories = Category::withCount('products')
            ->take(4)
            ->get();

        return view('frontend.home', compact('featuredProducts', 'categories'));
    }
}
